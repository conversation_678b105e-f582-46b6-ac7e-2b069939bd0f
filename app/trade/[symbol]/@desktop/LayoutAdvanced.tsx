"use client";

import React from "react";
import { useParams } from "next/navigation";
import TradingView from "@/components/TradingView";
import { PairTicker } from "@/components/PairTicker";
import { OrderBook } from "@/components/OrderBook";
import { OrderForm } from "@/components/OrderForm";
import { OrdersExchange } from "@/components/OrderExchange";
import { PairTradeExplorer } from "@/components/RecentTrade/PairTradeExplorer";
import { Assets } from "@/components/Assets";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";

export const LayoutAdvanced = () => {
  const params = useParams();
  const symbol = params?.symbol ? String(params.symbol) : "";
  const {
    isHasChart,
    isHasOrderBook,
    isHasTrades,
    isHasOpenOrders,
    isHasPlaceOrder,
  } = useSelector((state: RootState) => state.metadata.settingsLayout);

  return (
    <div>
      <div className="flex w-full">
        <div className="border-white-100 flex h-max flex-1 flex-col border-r">
          <PairTicker />
          {isHasChart && (
            <div className="border-white-100 h-[564px] border-b">
              <TradingView symbol={symbol as string} />
            </div>
          )}
          {isHasOpenOrders && (
            <div className={`border-white-100 ${isHasChart ? "" : "border-b"}`}>
              <OrdersExchange isInPair />
            </div>
          )}
        </div>
        <div className="border-white-100 h-max w-[300px] border-x">
          {isHasOrderBook && (
            <div className="border-white-100 border-b">
              <OrderBook />
            </div>
          )}
          {isHasTrades && (
            <div
              className={`border-white-100 ${isHasOrderBook ? "" : "border-b"}`}
            >
              <PairTradeExplorer />
            </div>
          )}
        </div>
        <div className="border-white-100 h-max w-[300px] border-l">
          {isHasPlaceOrder && <OrderForm />}
          <Assets />
        </div>
      </div>
    </div>
  );
};

"use client";

import { useState, useEffect, useCallback } from "react";
import { SimpleOrder } from "./simple-order"; // Updated with initTicker prop
import { AdvancedOrder } from "./advanced-order";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { SelectStopOrder } from "./components/SelectStopOrder";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import rf from "@/services/RequestFactory";
import { Ticker } from "@/types/pair";
import { TYPE_LAYOUT } from "../../constants/common";

export enum EOrderType {
  LIMIT = "LIMIT",
  MARKET = "MARKET",
  STOP_LIMIT = "STOP_LIMIT",
  STOP_MARKET = "STOP_MARKET",
}

export const OrderForm = () => {
  const [orderType, setOrderType] = useState<EOrderType>(EOrderType.LIMIT);
  const [initTicker, setInitTicker] = useState<Ticker | null>(null);
  const { pairSetting } = usePairContext();

  const { type } = useSelector(
    (state: RootState) => state.metadata.settingsLayout
  );
  const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;

  const fetchInitialTickerPrice = useCallback(async () => {
    if (!pairSetting?.symbol) return;

    try {
      const tickerData = await rf
        .getRequest("TickerRequest")
        .getTicker(pairSetting.symbol);

      if (tickerData?.data) {
        const ticker: Ticker = {
          symbol: tickerData.data.symbol,
          lastPrice: tickerData.data.lastPrice,
          highPrice: tickerData.data.highPrice,
          lowPrice: tickerData.data.lowPrice,
          priceChangePercent: tickerData.data.priceChangePercent,
          priceChange: tickerData.data.priceChange,
          baseVolume: tickerData.data.baseVolume,
          quoteVolume: tickerData.data.quoteVolume,
          timestamp: Date.now(),
          isUp: parseFloat(tickerData.data.priceChangePercent || "0") >= 0,
          isUp24h: parseFloat(tickerData.data.priceChangePercent || "0") >= 0,
        };
        setInitTicker(ticker);
      }
    } catch (error) {
      console.log("Error fetching initial ticker:", error);
    }
  }, [pairSetting?.symbol]);

  useEffect(() => {
    if (pairSetting?.symbol) {
      fetchInitialTickerPrice();
    }
  }, [pairSetting?.symbol, fetchInitialTickerPrice]);

  return (
    <div className="min-h-[320px] w-full">
      <div className="flex items-center justify-between">
        <div
          className={`body-md-medium-14 flex ${
            isLayoutAdvanced ? "border-white-100 border-b" : ""
          }`}
        >
          <div
            onClick={() => setOrderType(EOrderType.LIMIT)}
            className={`cursor-pointer py-2 ${
              isLayoutAdvanced ? "px-2" : "px-3"
            } ${
              orderType === EOrderType.LIMIT
                ? "border-white-1000 border-b"
                : "text-white-500"
            }`}
          >
            Limit
          </div>
          <div
            onClick={() => setOrderType(EOrderType.MARKET)}
            className={`cursor-pointer  ${
              isLayoutAdvanced ? "px-2" : "px-3"
            } py-2 ${
              orderType === EOrderType.MARKET
                ? "border-white-1000 border-b"
                : "text-white-500"
            }`}
          >
            Market
          </div>

          <div
            className={`cursor-pointer ${
              isLayoutAdvanced ? "px-2" : "px-3"
            } py-2 ${
              orderType === EOrderType.STOP_LIMIT ||
              orderType === EOrderType.STOP_MARKET
                ? "border-white-1000 border-b"
                : "text-white-500"
            }`}
          >
            <SelectStopOrder
              orderType={orderType}
              setOrderType={setOrderType}
            />
          </div>
        </div>

        {/* {!isLayoutAdvanced && (
          <div className="body-sm-regular-12 flex gap-4">
            <div className="flex cursor-pointer items-center gap-1">
              <AutoInvestIcon />
              Auto-Invest
            </div>
            <div className="flex cursor-pointer items-center gap-1">
              <UnlockIcon />
              Buy With VND
            </div>
          </div>
        )} */}
      </div>

      {isLayoutAdvanced ? (
        <AdvancedOrder orderType={orderType} initTicker={initTicker} />
      ) : (
        <SimpleOrder orderType={orderType} initTicker={initTicker} />
      )}
    </div>
  );
};

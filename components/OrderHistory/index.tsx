"use client";

import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import { AppDataTableRealtime } from "@/components";
import { CheckboxIcon, CheckboxCheckedIcon, FilterIcon } from "@/assets/icons";
import { useMediaQuery } from "react-responsive";
import { useWindowSize, useHistoryFilter } from "@/hooks";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import { SelectOrderType } from "../OrderExchange/components/SelectOrderType";
import { OrderHistoryItem } from "./components/OrderHistoryItem";
import rf from "@/services/RequestFactory";
import { EOrderStatus, TOpenOrder, TOrderHistory } from "@/types/order";
import { ModalHistoryFilter } from "../../modals/ModalHistoryFilter";
import { HistoryFilterDesktop } from "../HistoryFilter";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TYPE_LAYOUT } from "../../constants/common";
import moment from "moment";

export enum EOrderSideParam {
  All = "All",
  BUY = "Buy",
  SELL = "Sell",
}

export const OPTIONS_SIDE = [
  {
    label: "All",
    value: EOrderSideParam.All,
  },
  {
    label: "Buy",
    value: EOrderSideParam.BUY,
  },
  {
    label: "Sell",
    value: EOrderSideParam.SELL,
  },
];

export const TableOrderHistory = memo(
  ({ isInPair = false }: { isInPair: boolean }) => {
    // Use shared filter hook
    const { state: filterState, actions: filterActions } = useHistoryFilter({
      includeQuoteAsset: true,
      includeOrderType: true,
      includeSortBy: true,
    });

    const dataTableRef = useRef<any>(null);
    const [isHideAllCancel, setIsHideAllCancel] = useState<boolean>(false);

    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();

    const { type: typeLayout } = useSelector(
      (state: RootState) => state.metadata.settingsLayout
    );
    const isLayoutAdvanced = typeLayout === TYPE_LAYOUT.ADVANCED;

    const { activePairSettings } = useSelector(
      (state: RootState) => state.pairSettings
    );

    const baseAssetOptions = useMemo(() => {
      const baseAssets = new Set<string>();
      activePairSettings.forEach((pair: { baseAsset?: string }) => {
        if (pair.baseAsset) {
          baseAssets.add(pair.baseAsset);
        }
      });

      const sortedBaseAssets = Array.from(baseAssets).sort();
      const options = sortedBaseAssets.map((asset) => ({
        label: asset,
        value: asset,
      }));

      return [{ label: "All", value: "" }, ...options];
    }, [activePairSettings]);

    const quoteAssetOptions = useMemo(() => {
      const quoteAssets = new Set<string>();
      activePairSettings.forEach((pair: { quoteAsset?: string }) => {
        if (pair.quoteAsset) {
          quoteAssets.add(pair.quoteAsset);
        }
      });

      const sortedQuoteAssets = Array.from(quoteAssets).sort();
      const options = sortedQuoteAssets.map((asset) => ({
        label: asset,
        value: asset,
      }));

      return [{ label: "All", value: "" }, ...options];
    }, [activePairSettings]);

    const getData = useCallback(
      async (params: any) => {
        try {
          // Only fetch data when both start and end dates are provided
          if (!filterState.startDate || !filterState.endDate) {
            return {
              cursor: null,
              data: [],
            };
          }

          const orderParams = {
            ...params,
            side: filterState.side,
            type: filterState.orderType,
            startTime: moment(filterState.startDate).startOf("day")?.valueOf(),
            endTime: moment(filterState.endDate).endOf("day").valueOf(),
            baseAsset: filterState.base || undefined, // Include base filter if selected
            quoteAsset: filterState.quote || undefined, // Include quote filter if selected
          };

          const { docs, cursor } = await rf
            .getRequest("OrderRequest")
            .getOrders(orderParams);

          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getData error");
          return { cursor: null, data: [] };
        }
      },
      [
        filterState.side,
        filterState.orderType,
        filterState.startDate,
        filterState.endDate,
        filterState.base,
        filterState.quote,
      ]
    );

    const handleSearch = useCallback(() => {
      if (dataTableRef.current) {
        dataTableRef.current.filter();
      }
    }, []);

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }

        if (windowHeight > 1310) {
          return windowHeight - 1010;
        }
        return 300;
      }

      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return windowHeight - 250;
    }, [windowHeight, isMobile, isInPair, isLayoutAdvanced]);

    return (
      <>
        {!isInPair && (
          <HistoryFilterDesktop
            state={filterState}
            actions={filterActions}
            baseAssetOptions={baseAssetOptions}
            quoteAssetOptions={quoteAssetOptions}
            isInPair={isInPair}
            includeQuoteAsset={true}
            includeOrderType={true}
            onSearch={handleSearch}
          />
        )}

        {isInPair && (
          <HistoryFilterDesktop
            state={filterState}
            actions={filterActions}
            baseAssetOptions={baseAssetOptions}
            quoteAssetOptions={quoteAssetOptions}
            isInPair={isInPair}
            includeQuoteAsset={true}
            includeOrderType={true}
            onSearch={handleSearch}
          />
        )}

        <div className="flex items-center justify-between px-4 py-2 lg:hidden">
          <div
            className="body-md-regular-14 flex items-center gap-2"
            onClick={() => setIsHideAllCancel(!isHideAllCancel)}
          >
            {isHideAllCancel ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
            Hide All Cancel
          </div>

          <div onClick={() => filterActions.setIsShowModalFilter(true)}>
            <FilterIcon />
          </div>
        </div>

        <div className="w-full">
          <AppDataTableRealtime
            minWidth={1108}
            minHeight={300}
            height={tableHeight}
            ref={dataTableRef}
            handleAddNewItem={{
              broadcastName: BROADCAST_EVENTS.ORDER_UPDATED,
              fieldKey: "id",
              formatter: (data: TOpenOrder) => {
                if (
                  [EOrderStatus.NEW, EOrderStatus.PARTIALLY_FILLED].includes(
                    data.status
                  )
                ) {
                  return null;
                }

                return data;
              },
            }}
            getData={getData}
            shouldAutoFetchOnInit
            overrideBodyClassName="w-full"
            renderHeader={() => {
              if (isMobile) {
                return null;
              }
              return (
                <>
                  <div className="flex w-full items-center">
                    <div className="body-sm-regular-12 text-white-500 flex w-[10%] min-w-[90px] items-center px-2 py-1.5 ">
                      Date
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      Pair
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      <SelectOrderType
                        orderType={filterState.orderType}
                        setOrderType={filterActions.setOrderType}
                      />
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[70px] px-2 py-1.5 text-left">
                      <SelectSideOrder
                        side={filterState.side}
                        setSide={filterActions.setSide}
                      />
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      Average
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      Price
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      Excuted
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[9%] min-w-[80px] px-2 py-1.5 text-left">
                      Amount
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[80px] px-2 py-1.5 text-left">
                      Total
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[8%] min-w-[80px] px-2 py-1.5 text-left">
                      Status
                    </div>
                    <div className="body-sm-regular-12 text-white-500 w-[8%] min-w-[120px] px-2 py-1.5 text-center">
                      Trigger Condition
                    </div>
                  </div>
                </>
              );
            }}
            renderRow={(item: TOrderHistory, index: number) => {
              return <OrderHistoryItem key={index} order={item} />;
            }}
          />
        </div>

        {filterState.isShowModalFilter && (
          <ModalHistoryFilter
            isOpen={filterState.isShowModalFilter}
            state={filterState}
            actions={filterActions}
            baseAssetOptions={baseAssetOptions}
            quoteAssetOptions={quoteAssetOptions}
            includeQuoteAsset={true}
            includeOrderType={true}
            title="Filter Order History"
          />
        )}
      </>
    );
  }
);

TableOrderHistory.displayName = "TableOrderHistory";

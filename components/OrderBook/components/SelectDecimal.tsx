import { usePairContext } from "@/app/trade/[symbol]/provider";
import { ChevronDownIcon } from "@/assets/icons";
import AppNumber from "@/components/AppNumber";
import { AppPopover } from "@/components/AppPopover";
import React, { useEffect, useState } from "react";
import rf from "@/services/RequestFactory";

const OPTIONS_DECIMAL = [0.001, 0.01, 0.1, 1, 10, 100];

const SelectDecimal = ({
  setDecimals,
  decimals,
}: {
  setDecimals: (value: number) => void;
  decimals: number;
}) => {
  const [isShowDecimal, setShowDecimal] = useState<boolean>(false);
  const { pairSetting } = usePairContext();
  const [decimalOptions, setDecimalOptions] = useState(OPTIONS_DECIMAL);

  useEffect(() => {
    if (!pairSetting?.symbol) {
      return;
    }

    const initPriceGroup = async () => {
      try {
        const data = await rf
          .getRequest("OrderbookRequest")
          .getOrderbookPriceGroup(pairSetting?.symbol);
        const priceGroup = data?.data || [];

        if (!priceGroup.length) {
          console.log("no price group");
          return;
        }

        const priceGroupDecimal = priceGroup.map(
          (item: { price_group: number }) => item.price_group
        );
        setDecimalOptions(priceGroupDecimal);
        setDecimals(priceGroupDecimal[0]);
      } catch (error) {
        console.error("initPriceGroup error", error);
      }
    };

    initPriceGroup();
  }, [pairSetting?.symbol, setDecimals]);

  return (
    <AppPopover
      trigger={
        <div className="body-md-medium-14 flex cursor-pointer items-center gap-1">
          <AppNumber
            value={decimals}
            decimals={pairSetting?.pricePrecision}
            isFormatLargeNumber={false}
          />
          <ChevronDownIcon />
        </div>
      }
      onClose={() => setShowDecimal(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[70px] flex-col gap-2 rounded-[8px] p-3"
        >
          {decimalOptions.map((item) => {
            return (
              <div
                key={item}
                className={`body-sm-medium-12 hover:text-white-1000 cursor-pointer ${
                  item === decimals ? "text-white-1000" : "text-white-500"
                }`}
                onClick={() => {
                  setDecimals(item);
                  setShowDecimal(false);
                }}
              >
                <AppNumber
                  value={item}
                  decimals={pairSetting?.pricePrecision}
                  isFormatLargeNumber={false}
                />
              </div>
            );
          })}
        </div>
      }
      isOpen={isShowDecimal}
      onToggle={() => setShowDecimal(!isShowDecimal)}
    />
  );
};

export default SelectDecimal; // Should be name SelectPriceGroup
